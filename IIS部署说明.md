# 微信公众号验证服务器 - IIS 部署说明

## 📋 部署前准备

### 1. 服务器环境要求
- Windows Server 2016 或更高版本
- IIS 10.0 或更高版本
- ASP.NET Core Runtime 8.0 或更高版本

### 2. 安装 ASP.NET Core Runtime
1. 下载并安装 [ASP.NET Core Runtime 8.0](https://dotnet.microsoft.com/download/dotnet/8.0)
2. 选择 "ASP.NET Core Runtime" 版本（不是 SDK）
3. 安装 "Hosting Bundle" 版本以获得 IIS 支持

### 3. 启用 IIS 功能
在 Windows 功能中启用以下组件：
- Internet Information Services
- Web Management Tools
- World Wide Web Services
- Application Development Features
  - ASP.NET 4.8
  - .NET Extensibility 4.8

## 🚀 部署步骤

### 步骤 1: 发布应用程序
```bash
# 运行发布脚本
publish.bat
```

或手动执行：
```bash
dotnet publish -c Release -o publish --self-contained false
```

### 步骤 2: 配置 IIS

1. **创建应用程序池**
   - 打开 IIS 管理器
   - 右键点击 "应用程序池" → "添加应用程序池"
   - 名称：`WeChatServerPool`
   - .NET CLR 版本：`无托管代码`
   - 托管管道模式：`集成`

2. **创建网站**
   - 右键点击 "网站" → "添加网站"
   - 网站名称：`WeChatServer`
   - 应用程序池：选择刚创建的 `WeChatServerPool`
   - 物理路径：指向 `publish` 文件夹的完整路径
   - 端口：`3000`（或其他可用端口）

3. **设置权限**
   - 右键点击网站物理路径文件夹
   - 属性 → 安全 → 编辑
   - 添加 `IIS_IUSRS` 用户组，给予 "读取和执行" 权限

### 步骤 3: 测试部署

1. **启动网站**
   - 在 IIS 管理器中启动网站
   - 检查应用程序池状态

2. **测试访问**
   ```
   http://localhost:3000/wechat?signature=test&timestamp=test&nonce=test&echostr=test
   ```

## 🔧 配置说明

### web.config 配置
```xml
<aspNetCore processPath="dotnet" 
            arguments=".\WeChatServer.dll" 
            stdoutLogEnabled="true" 
            stdoutLogFile=".\logs\stdout" 
            hostingModel="inprocess" />
```

### 重要配置项：
- `processPath`: 指向 dotnet.exe
- `arguments`: 指向应用程序 DLL
- `stdoutLogEnabled`: 启用日志记录
- `hostingModel`: 使用进程内托管模式

## 📝 日志和监控

### 查看日志
- 应用程序日志：`publish\logs\stdout_*.log`
- IIS 日志：`C:\inetpub\logs\LogFiles\W3SVC1\`
- Windows 事件日志：应用程序和服务日志 → Microsoft → Windows → IIS-AspNetCoreModule

### 常见问题排查

1. **500.19 错误**
   - 检查 web.config 语法
   - 确认 ASP.NET Core Module 已安装

2. **500.30 错误**
   - 检查应用程序池配置
   - 确认 .NET CLR 版本设置为 "无托管代码"

3. **502.5 错误**
   - 检查 ASP.NET Core Runtime 是否已安装
   - 查看 stdout 日志文件

## 🌐 生产环境配置

### 安全配置
1. 移除详细错误信息（生产环境）
2. 配置 HTTPS
3. 设置防火墙规则
4. 配置负载均衡（如需要）

### 性能优化
1. 启用输出缓存
2. 配置压缩
3. 设置适当的应用程序池回收策略

## 📞 微信公众平台配置

在微信公众平台后台配置：
- **服务器地址(URL)**: `http://你的域名:3000/wechat`
- **令牌(Token)**: `NursingManagement_Test`
- **消息加解密方式**: 明文模式

## ✅ 验证部署成功

部署成功后，您应该能够：
1. 访问服务器 URL 并收到参数验证响应
2. 在微信公众平台成功验证服务器配置
3. 查看 IIS 日志确认请求正常处理
