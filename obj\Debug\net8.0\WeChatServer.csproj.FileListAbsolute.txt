C:\新建文件夹\bin\Debug\net8.0\package-lock.json
C:\新建文件夹\bin\Debug\net8.0\package.json
C:\新建文件夹\bin\Debug\net8.0\WeChatServer.exe
C:\新建文件夹\bin\Debug\net8.0\WeChatServer.deps.json
C:\新建文件夹\bin\Debug\net8.0\WeChatServer.runtimeconfig.json
C:\新建文件夹\bin\Debug\net8.0\WeChatServer.dll
C:\新建文件夹\bin\Debug\net8.0\WeChatServer.pdb
C:\新建文件夹\obj\Debug\net8.0\WeChatServer.GeneratedMSBuildEditorConfig.editorconfig
C:\新建文件夹\obj\Debug\net8.0\WeChatServer.AssemblyInfoInputs.cache
C:\新建文件夹\obj\Debug\net8.0\WeChatServer.AssemblyInfo.cs
C:\新建文件夹\obj\Debug\net8.0\WeChatServer.csproj.CoreCompileInputs.cache
C:\新建文件夹\obj\Debug\net8.0\WeChatServer.MvcApplicationPartsAssemblyInfo.cache
C:\新建文件夹\obj\Debug\net8.0\staticwebassets.build.json
C:\新建文件夹\obj\Debug\net8.0\staticwebassets.development.json
C:\新建文件夹\obj\Debug\net8.0\staticwebassets\msbuild.WeChatServer.Microsoft.AspNetCore.StaticWebAssets.props
C:\新建文件夹\obj\Debug\net8.0\staticwebassets\msbuild.build.WeChatServer.props
C:\新建文件夹\obj\Debug\net8.0\staticwebassets\msbuild.buildMultiTargeting.WeChatServer.props
C:\新建文件夹\obj\Debug\net8.0\staticwebassets\msbuild.buildTransitive.WeChatServer.props
C:\新建文件夹\obj\Debug\net8.0\staticwebassets.pack.json
C:\新建文件夹\obj\Debug\net8.0\scopedcss\bundle\WeChatServer.styles.css
C:\新建文件夹\obj\Debug\net8.0\WeChatServer.dll
C:\新建文件夹\obj\Debug\net8.0\refint\WeChatServer.dll
C:\新建文件夹\obj\Debug\net8.0\WeChatServer.pdb
C:\新建文件夹\obj\Debug\net8.0\WeChatServer.genruntimeconfig.cache
C:\新建文件夹\obj\Debug\net8.0\ref\WeChatServer.dll
