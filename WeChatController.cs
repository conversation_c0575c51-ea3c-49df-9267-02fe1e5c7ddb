/*
 * relative     : \nursing-management-mobile\WeChatServer\WeChatController.cs
 * Author       : 张现忠
 * Date         : 2025-07-06 17:45
 * LastEditors  : 张现忠
 * LastEditTime : 2025-07-06 17:45
 * Description  : 微信公众号 Token 验证控制器
 * CodeIterationRecord:
 */

using Microsoft.AspNetCore.Mvc;
using System.Security.Cryptography;
using System.Text;

namespace WeChatServer.Controllers
{
    [ApiController]
    [Route("[controller]")]
    public class WeChatController : ControllerBase
    {
        // 微信公众号配置的 Token（需和微信公众平台一致）
        private const string TOKEN = "NursingManagement_Test"; // 替换成你的 Token

        private readonly ILogger<WeChatController> _logger;

        public WeChatController(ILogger<WeChatController> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 验证微信服务器 Token
        /// </summary>
        /// <param name="signature">微信加密签名</param>
        /// <param name="timestamp">时间戳</param>
        /// <param name="nonce">随机数</param>
        /// <param name="echostr">随机字符串</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult Get(string? signature, string? timestamp, string? nonce, string? echostr)
        {
            try
            {
                // 1. 获取微信传来的参数
                _logger.LogInformation("GET /wechat - Client IP: {ClientIP}", HttpContext.Connection.RemoteIpAddress);
                _logger.LogInformation("Parameters: signature={Signature}, timestamp={Timestamp}, nonce={Nonce}, echostr={Echostr}",
                    signature, timestamp, nonce, echostr);

                // 2. 检查参数是否存在
                if (string.IsNullOrEmpty(signature) ||
                    string.IsNullOrEmpty(timestamp) ||
                    string.IsNullOrEmpty(nonce) ||
                    string.IsNullOrEmpty(echostr))
                {
                    _logger.LogWarning("Missing parameters in WeChat verification request");
                    return BadRequest("Missing parameters");
                }

                // 3. 按照微信规则计算 signature
                var arr = new[] { TOKEN, timestamp, nonce };
                Array.Sort(arr);
                var str = string.Join("", arr);
                var sha1 = ComputeSha1Hash(str);

                _logger.LogDebug("Computed SHA1: {ComputedSha1}", sha1);
                _logger.LogDebug("Received signature: {ReceivedSignature}", signature);

                // 4. 比较计算出的 signature 和微信传来的 signature
                if (sha1.Equals(signature, StringComparison.OrdinalIgnoreCase))
                {
                    // 验证成功，返回 echostr
                    _logger.LogInformation("WeChat signature verification successful");
                    return Ok(echostr);
                }
                else
                {
                    // 验证失败
                    _logger.LogWarning("WeChat signature verification failed");
                    return StatusCode(403, "Invalid signature");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing WeChat verification request");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 计算 SHA1 哈希值
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <returns>SHA1 哈希值（小写十六进制）</returns>
        private static string ComputeSha1Hash(string input)
        {
            using var sha1 = SHA1.Create();
            var inputBytes = Encoding.UTF8.GetBytes(input);
            var hashBytes = sha1.ComputeHash(inputBytes);
            
            // 转换为小写十六进制字符串
            var sb = new StringBuilder();
            foreach (var b in hashBytes)
            {
                sb.Append(b.ToString("x2"));
            }
            return sb.ToString();
        }
    }
}
