/*
 * relative     : \新建文件夹\server.js
 * Author       : 张现忠
 * Date         : 2025-07-06 17:31
 * LastEditors  : 张现忠
 * LastEditTime : 2025-07-09 10:44
 * Description  :
 * CodeIterationRecord:
 */
const express = require("express");
const crypto = require("crypto");
const axios = require("axios");

const app = express();
const port = process.env.PORT || 3001; // 你的服务器端口（需和微信公众平台配置一致）

// 微信公众号配置的 Token（需和微信公众平台一致）
const TOKEN = "NursingManagement_Test"; // 替换成你的 Token

// 微信公众号配置（需要在微信公众平台获取）
const APPID = "wx2990219697ef8c2a"; // 替换成你的 AppID
const APPSECRET = "d69be74f018e775ed27ba1e62003dc1a"; // 替换成你的 AppSecret

// 解析 JSON 请求体
app.use(express.json());

// 验证微信服务器 Token
app.get("/wechat", (req, res) => {
  // 1. 获取微信传来的参数
  const { signature, timestamp, nonce, echostr } = req.query;

  // 2. 检查参数是否存在
  if (!signature || !timestamp || !nonce || !echostr) {
    console.log(
      `Server running at http://localhost:${port}`,
      signature,
      timestamp,
      nonce,
      echostr
    );
    return res.status(400).send("Missing parameters");
  }

  // 3. 按照微信规则计算 signature
  const arr = [TOKEN, timestamp, nonce].sort();
  const str = arr.join("");
  const sha1 = crypto.createHash("sha1").update(str).digest("hex");

  // 4. 比较计算出的 signature 和微信传来的 signature
  if (sha1 === signature) {
    // 验证成功，返回 echostr
    res.send(echostr);
  } else {
    // 验证失败
    res.status(403).send("Invalid signature");
  }
});

// ==================== 微信 JS-SDK 配置相关接口 ====================

// 1. 获取 access_token
async function getAccessToken() {
  try {
    const url = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${APPID}&secret=${APPSECRET}`;
    const res = await axios.get(url);

    if (res.data.errcode) {
      throw new Error(`获取 access_token 失败: ${res.data.errmsg}`);
    }

    console.log(`获取 access_token 成功: ${res.data.access_token}`);
    return res.data.access_token;
  } catch (error) {
    console.error("获取 access_token 错误:", error.message);
    throw error;
  }
}

// 2. 获取 jsapi_ticket
async function getJsapiTicket(accessToken) {
  try {
    const url = `https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=${accessToken}&type=jsapi`;
    const res = await axios.get(url);

    if (res.data.errcode !== 0) {
      throw new Error(`获取 jsapi_ticket 失败: ${res.data.errmsg}`);
    }

    console.log(`获取 jsapi_ticket 成功: ${res.data.ticket}`);
    return res.data.ticket;
  } catch (error) {
    console.error("获取 jsapi_ticket 错误:", error.message);
    throw error;
  }
}

// 3. 生成签名
function generateSignature(jsapiTicket, nonceStr, timestamp, url) {
  const str = `jsapi_ticket=${jsapiTicket}&noncestr=${nonceStr}&timestamp=${timestamp}&url=${url}`;
  console.log(`签名字符串: ${str}`);

  const signature = crypto.createHash("sha1").update(str).digest("hex");
  console.log(`生成的签名: ${signature}`);

  return signature;
}

// 4. 提供给前端的接口 - 获取微信 JS-SDK 配置
app.get("/get-wx-config", async (req, res) => {
  try {
    console.log(`${new Date().toISOString()} - GET /get-wx-config`);

    // 检查必要的配置
    if (APPID === "your_app_id" || APPSECRET === "your_app_secret") {
      return res.status(500).json({
        error: "请先配置正确的 APPID 和 APPSECRET",
      });
    }

    // 获取前端传来的 URL
    const url = req.query.url;
    if (!url) {
      return res.status(400).json({
        error: "缺少 url 参数",
      });
    }

    console.log(`请求的页面 URL: ${url}`);

    // 获取 access_token
    const accessToken = await getAccessToken();

    // 获取 jsapi_ticket
    const jsapiTicket = await getJsapiTicket(accessToken);

    // 生成随机字符串和时间戳
    const nonceStr = Math.random().toString(36).substr(2, 15);
    const timestamp = Math.floor(Date.now() / 1000);

    // 生成签名
    const signature = generateSignature(jsapiTicket, nonceStr, timestamp, url);

    // 返回配置信息
    const config = {
      Code: 1,
      Data: {
        appId: APPID,
        timestamp,
        nonceStr,
        signature,
      },
      Message: "成功",
      MessageDev: null,
    };

    console.log("返回微信配置:", JSON.stringify(config, null, 2));
    res.json(config);
  } catch (err) {
    console.error("获取微信配置失败:", err.message);
    res.status(500).json({
      error: `获取微信配置失败: ${err.message}`,
    });
  }
});

// 启动服务器
const server = app.listen(port, () => {
  console.log(`Server running at http://localhost:${port}`);
  console.log(
    `Verify URL: http://localhost:${port}/wechat?signature=xxx&timestamp=xxx&nonce=xxx&echostr=xxx`
  );
  console.log(
    `WeChat JS-SDK Config URL: http://localhost:${port}/get-wx-config?url=YOUR_PAGE_URL`
  );
  console.log(`请确保已正确配置 APPID 和 APPSECRET`);
});

// 错误处理
server.on("error", (err) => {
  console.error("服务器启动失败:", err);
});

// 优雅关闭
process.on("SIGINT", () => {
  console.log("\n正在关闭服务器...");
  server.close(() => {
    console.log("服务器已关闭");
    process.exit(0);
  });
});

process.on("SIGTERM", () => {
  console.log("收到 SIGTERM 信号，正在关闭服务器...");
  server.close(() => {
    console.log("服务器已关闭");
    process.exit(0);
  });
});
