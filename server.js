/*
 * relative     : \新建文件夹\server.js
 * Author       : 张现忠
 * Date         : 2025-07-06 17:31
 * LastEditors  : 张现忠
 * LastEditTime : 2025-07-07 09:53
 * Description  :
 * CodeIterationRecord:
 */
const express = require("express");
const crypto = require("crypto");

const app = express();
const port = 3000; // 你的服务器端口（需和微信公众平台配置一致）

// 微信公众号配置的 Token（需和微信公众平台一致）
const TOKEN = "NursingManagement_Test"; // 替换成你的 Token

// 验证微信服务器 Token
app.get("/wechat", (req, res) => {
  // 1. 获取微信传来的参数
  const { signature, timestamp, nonce, echostr } = req.query;

  // 2. 检查参数是否存在
  if (!signature || !timestamp || !nonce || !echostr) {
    console.log(
      `Server running at http://localhost:${port}`,
      signature,
      timestamp,
      nonce,
      echostr
    );
    return res.status(400).send("Missing parameters");
  }

  // 3. 按照微信规则计算 signature
  const arr = [TOKEN, timestamp, nonce].sort();
  const str = arr.join("");
  const sha1 = crypto.createHash("sha1").update(str).digest("hex");

  // 4. 比较计算出的 signature 和微信传来的 signature
  if (sha1 === signature) {
    // 验证成功，返回 echostr
    res.send(echostr);
  } else {
    // 验证失败
    res.status(403).send("Invalid signature");
  }
});

// 启动服务器
app.listen(port, () => {
  console.log(`Server running at http://localhost:${port}`);
  console.log(
    `Verify URL: http://localhost:${port}/wechat?signature=xxx&timestamp=xxx&nonce=xxx&echostr=xxx`
  );
});
