<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
      </handlers>
      <aspNetCore processPath="dotnet" 
                  arguments=".\WeChatServer.dll" 
                  stdoutLogEnabled="true" 
                  stdoutLogFile=".\logs\stdout" 
                  hostingModel="inprocess" />
      
      <!-- 启用详细错误信息 -->
      <httpErrors errorMode="Detailed" />
      
      <!-- 设置默认文档 -->
      <defaultDocument>
        <files>
          <clear />
        </files>
      </defaultDocument>
      
      <!-- URL 重写规则（可选） -->
      <rewrite>
        <rules>
          <!-- 可以在这里添加 URL 重写规则 -->
        </rules>
      </rewrite>
      
    </system.webServer>
  </location>
</configuration>
