@echo off
echo Testing WeChat Server deployment...
echo.

REM 测试基本连接
echo Testing basic connectivity...
curl -i "http://localhost:3000/wechat" 2>nul
if %errorlevel% neq 0 (
    echo ERROR: Cannot connect to server. Make sure IIS is running and the site is configured correctly.
    goto :end
)

echo.
echo Testing with missing parameters (should return 400)...
curl -i "http://localhost:3000/wechat" 2>nul

echo.
echo Testing with invalid signature (should return 403)...
curl -i "http://localhost:3000/wechat?signature=invalid&timestamp=123456&nonce=test&echostr=hello" 2>nul

echo.
echo Testing with valid signature...
REM 这里需要计算正确的签名，暂时跳过
echo Note: For valid signature test, use WeChat developer tools or calculate SHA1 manually.

echo.
echo Test completed!

:end
pause
