<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信 JS-SDK 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            background-color: #1aad19;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 10px 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .button:hover {
            background-color: #179b16;
        }
        .button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .config-display {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>微信 JS-SDK 测试页面</h1>
        
        <div id="status" class="status info">
            正在初始化微信 JS-SDK...
        </div>
        
        <div class="config-display" id="configDisplay">
            等待获取配置...
        </div>
        
        <div>
            <button class="button" id="initBtn" onclick="initWechat()">初始化微信配置</button>
            <button class="button" id="chooseImageBtn" onclick="chooseImage()" disabled>选择图片</button>
            <button class="button" id="getLocationBtn" onclick="getLocation()" disabled>获取位置</button>
            <button class="button" id="shareBtn" onclick="setShare()" disabled>设置分享</button>
        </div>
        
        <div id="result" style="margin-top: 20px;"></div>
    </div>

    <!-- 引入微信 JS-SDK -->
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    
    <script>
        let isWechatReady = false;
        
        // 获取当前页面完整 URL（不包含 # 后面的部分）
        function getCurrentUrl() {
            return window.location.href.split('#')[0];
        }
        
        // 显示状态信息
        function showStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.className = `status ${type}`;
            statusEl.textContent = message;
        }
        
        // 显示结果
        function showResult(message, type = 'info') {
            const resultEl = document.getElementById('result');
            resultEl.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        // 更新按钮状态
        function updateButtons(enabled) {
            const buttons = ['chooseImageBtn', 'getLocationBtn', 'shareBtn'];
            buttons.forEach(id => {
                document.getElementById(id).disabled = !enabled;
            });
            isWechatReady = enabled;
        }
        
        // 初始化微信配置
        async function initWechat() {
            try {
                showStatus('正在获取微信配置...', 'info');
                
                const currentUrl = getCurrentUrl();
                console.log('当前页面 URL:', currentUrl);
                
                // 调用后端接口获取配置
                const response = await fetch(`/get-wx-config?url=${encodeURIComponent(currentUrl)}`);
                const config = await response.json();
                
                if (!response.ok) {
                    throw new Error(config.error || '获取配置失败');
                }
                
                // 显示配置信息
                document.getElementById('configDisplay').textContent = JSON.stringify(config, null, 2);
                
                console.log('微信配置:', config);
                
                // 配置微信 JS-SDK
                wx.config({
                    debug: true, // 开启调试模式
                    appId: config.appId,
                    timestamp: config.timestamp,
                    nonceStr: config.nonceStr,
                    signature: config.signature,
                    jsApiList: config.jsApiList
                });
                
                showStatus('微信配置已设置，等待验证...', 'info');
                
            } catch (error) {
                console.error('初始化失败:', error);
                showStatus(`初始化失败: ${error.message}`, 'error');
                updateButtons(false);
            }
        }
        
        // 微信配置成功回调
        wx.ready(function() {
            console.log('微信 JS-SDK 配置成功');
            showStatus('微信 JS-SDK 配置成功！', 'success');
            updateButtons(true);
        });
        
        // 微信配置失败回调
        wx.error(function(res) {
            console.error('微信 JS-SDK 配置失败:', res);
            showStatus(`微信 JS-SDK 配置失败: ${JSON.stringify(res)}`, 'error');
            updateButtons(false);
        });
        
        // 选择图片
        function chooseImage() {
            if (!isWechatReady) {
                showResult('请先初始化微信配置', 'error');
                return;
            }
            
            wx.chooseImage({
                count: 1,
                sizeType: ['original', 'compressed'],
                sourceType: ['album', 'camera'],
                success: function (res) {
                    const localIds = res.localIds;
                    showResult(`选择图片成功: ${localIds.join(', ')}`, 'success');
                },
                fail: function (res) {
                    showResult(`选择图片失败: ${JSON.stringify(res)}`, 'error');
                }
            });
        }
        
        // 获取位置
        function getLocation() {
            if (!isWechatReady) {
                showResult('请先初始化微信配置', 'error');
                return;
            }
            
            wx.getLocation({
                type: 'wgs84',
                success: function (res) {
                    const latitude = res.latitude;
                    const longitude = res.longitude;
                    showResult(`获取位置成功: 纬度 ${latitude}, 经度 ${longitude}`, 'success');
                },
                fail: function (res) {
                    showResult(`获取位置失败: ${JSON.stringify(res)}`, 'error');
                }
            });
        }
        
        // 设置分享
        function setShare() {
            if (!isWechatReady) {
                showResult('请先初始化微信配置', 'error');
                return;
            }
            
            wx.updateAppMessageShareData({
                title: '微信 JS-SDK 测试',
                desc: '这是一个微信 JS-SDK 功能测试页面',
                link: getCurrentUrl(),
                imgUrl: 'https://via.placeholder.com/300x300.png?text=WeChat+Test',
                success: function () {
                    showResult('分享配置设置成功', 'success');
                },
                fail: function (res) {
                    showResult(`分享配置设置失败: ${JSON.stringify(res)}`, 'error');
                }
            });
        }
        
        // 页面加载完成后自动初始化
        window.onload = function() {
            // 检查是否在微信环境中
            const ua = navigator.userAgent.toLowerCase();
            if (ua.indexOf('micromessenger') === -1) {
                showStatus('请在微信中打开此页面', 'error');
                return;
            }
            
            // 自动初始化
            setTimeout(initWechat, 1000);
        };
    </script>
</body>
</html>
