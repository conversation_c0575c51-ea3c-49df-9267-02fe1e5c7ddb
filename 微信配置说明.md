# 微信公众号 JS-SDK 配置说明

## 📋 配置前准备

### 1. 获取微信公众号信息
在微信公众平台（https://mp.weixin.qq.com/）获取以下信息：

- **AppID**: 公众号的唯一标识
- **AppSecret**: 公众号的密钥
- **Token**: 服务器配置的令牌

### 2. 配置服务器信息
在 `server.js` 文件中修改以下配置：

```javascript
// 微信公众号配置（需要在微信公众平台获取）
const APPID = "your_app_id"; // 替换成你的 AppID
const APPSECRET = "your_app_secret"; // 替换成你的 AppSecret
const TOKEN = "NursingManagement_Test"; // 替换成你的 Token
```

## 🚀 使用步骤

### 步骤 1: 启动服务器
```bash
node server.js
```

### 步骤 2: 配置微信公众平台
1. 登录微信公众平台
2. 进入 "开发" → "基本配置"
3. 设置服务器配置：
   - **URL**: `http://你的域名:3000/wechat`
   - **Token**: `NursingManagement_Test`
   - **EncodingAESKey**: 随机生成
   - **消息加解密方式**: 明文模式

### 步骤 3: 配置 JS 接口安全域名
1. 在微信公众平台进入 "设置" → "公众号设置" → "功能设置"
2. 设置 "JS接口安全域名" 为你的域名（不包含 http://）

## 🔧 API 接口说明

### 1. Token 验证接口
- **URL**: `GET /wechat`
- **参数**: signature, timestamp, nonce, echostr
- **用途**: 微信服务器验证

### 2. JS-SDK 配置接口
- **URL**: `GET /get-wx-config`
- **参数**: 
  - `url` (必需): 当前页面的完整 URL
- **返回**: 微信 JS-SDK 配置信息

**请求示例**:
```
GET /get-wx-config?url=http://example.com/page.html
```

**返回示例**:
```json
{
  "appId": "wx1234567890abcdef",
  "timestamp": 1641234567,
  "nonceStr": "abc123def456",
  "signature": "sha1_signature_string",
  "jsApiList": [
    "updateAppMessageShareData",
    "chooseImage",
    "previewImage",
    "uploadImage",
    "downloadImage",
    "getLocation",
    "openLocation"
  ]
}
```

## 🌐 前端使用示例

### 1. 引入微信 JS-SDK
```html
<script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
```

### 2. 获取配置并初始化
```javascript
// 获取当前页面 URL
const currentUrl = window.location.href.split('#')[0];

// 请求配置
fetch(`/get-wx-config?url=${encodeURIComponent(currentUrl)}`)
  .then(response => response.json())
  .then(config => {
    // 配置微信 JS-SDK
    wx.config({
      debug: false,
      appId: config.appId,
      timestamp: config.timestamp,
      nonceStr: config.nonceStr,
      signature: config.signature,
      jsApiList: config.jsApiList
    });
  });

// 配置成功回调
wx.ready(function() {
  console.log('微信 JS-SDK 配置成功');
  // 可以调用微信 API 了
});

// 配置失败回调
wx.error(function(res) {
  console.error('微信 JS-SDK 配置失败:', res);
});
```

### 3. 使用微信 API
```javascript
// 选择图片
wx.chooseImage({
  count: 1,
  sizeType: ['original', 'compressed'],
  sourceType: ['album', 'camera'],
  success: function (res) {
    console.log('选择的图片:', res.localIds);
  }
});

// 获取位置
wx.getLocation({
  type: 'wgs84',
  success: function (res) {
    console.log('位置信息:', res.latitude, res.longitude);
  }
});

// 设置分享
wx.updateAppMessageShareData({
  title: '分享标题',
  desc: '分享描述',
  link: window.location.href,
  imgUrl: 'http://example.com/share.jpg',
  success: function () {
    console.log('分享设置成功');
  }
});
```

## 🧪 测试页面

项目中包含了一个测试页面 `wechat-demo.html`，可以用来测试各种微信 JS-SDK 功能：

1. 将 `wechat-demo.html` 放到可通过域名访问的位置
2. 在微信中打开该页面
3. 点击各种按钮测试功能

## ⚠️ 注意事项

### 1. 域名要求
- 必须使用已备案的域名
- 不能使用 IP 地址
- 不能使用 localhost

### 2. HTTPS 要求
- 生产环境建议使用 HTTPS
- 某些 API（如获取位置）可能需要 HTTPS

### 3. 调试技巧
- 开启 `wx.config` 的 `debug: true` 可以看到详细错误信息
- 使用微信开发者工具进行调试
- 查看浏览器控制台的错误信息

### 4. 常见错误
- **invalid signature**: 签名错误，检查 URL 是否正确
- **invalid url domain**: 域名未配置或不匹配
- **the permission value is offline verifying**: 公众号未认证或接口权限不足

## 📝 日志查看

服务器会输出详细的日志信息，包括：
- access_token 获取过程
- jsapi_ticket 获取过程
- 签名生成过程
- 请求处理过程

可以通过这些日志来排查问题。

## 🔒 安全建议

1. **保护 AppSecret**: 不要在前端代码中暴露 AppSecret
2. **验证来源**: 可以添加 Referer 检查
3. **限制频率**: 添加请求频率限制
4. **缓存 Token**: access_token 有效期 2 小时，建议缓存使用
