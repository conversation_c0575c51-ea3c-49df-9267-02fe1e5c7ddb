/*
 * relative     : \nursing-management-mobile\WeChatServer\Program.cs
 * Author       : 张现忠
 * Date         : 2025-07-06 17:45
 * LastEditors  : 张现忠
 * LastEditTime : 2025-07-06 17:45
 * Description  : 微信公众号 Token 验证服务器 - C# 版本 (IIS 部署)
 * CodeIterationRecord:
 */

var builder = WebApplication.CreateBuilder(args);

// 添加服务到容器
builder.Services.AddControllers();

// 配置 IIS 集成
builder.Services.Configure<IISOptions>(options =>
{
    options.AutomaticAuthentication = false;
});

var app = builder.Build();

// 配置 HTTP 请求管道
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}

// 添加 IIS 支持
app.UseRouting();
app.MapControllers();

// 启动信息（在 IIS 中运行时不会显示在控制台）
var logger = app.Services.GetRequiredService<ILogger<Program>>();
logger.LogInformation("WeChat Token Verification Server started");
logger.LogInformation("Verify URL: /wechat?signature=xxx&timestamp=xxx&nonce=xxx&echostr=xxx");

app.Run();
