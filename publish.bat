@echo off
echo Publishing WeChat Server for IIS deployment...
echo.

REM 创建发布目录
if not exist "publish" mkdir publish

REM 发布应用程序
dotnet publish -c Release -o publish --self-contained false

echo.
echo Publishing completed!
echo.
echo Files are ready in the 'publish' folder for IIS deployment.
echo.
echo Next steps:
echo 1. Copy the 'publish' folder contents to your IIS website directory
echo 2. Make sure ASP.NET Core Runtime is installed on the server
echo 3. Configure IIS application pool to use 'No Managed Code'
echo 4. Set the website physical path to the publish folder
echo.
pause
